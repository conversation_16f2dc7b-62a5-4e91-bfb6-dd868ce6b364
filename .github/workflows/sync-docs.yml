name: Sync Mintlify Docs back to Github

on:
  push:
    branches:
      - main
    paths:
      - "docs/**.mdx"
  workflow_dispatch:

jobs:
  sync-docs:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Convert .mdx files to README.md
        run: |
          declare -A FILE_MAP=(
            ["docs/doc/developer/sdk/python.mdx"]="sdks/python/README.md"
            ["docs/doc/developer/sdk/ReactNative.mdx"]="sdks/react-native/README.md"
            ["docs/doc/developer/sdk/swift.mdx"]="sdks/swift/README.md"
            ["docs/doc/hardware/omiGlass.mdx"]="omiGlass/README.md"
          )

          for MDX_FILE in "${!FILE_MAP[@]}"; do
            README_FILE="${FILE_MAP[$MDX_FILE]}"
            
            # Create directory if it doesn't exist
            mkdir -p "$(dirname "$README_FILE")"

            if [ -f "$MDX_FILE" ]; then
              echo "Processing: $MDX_FILE → $README_FILE"

              awk 'BEGIN{printing=0} /^---$/ {printing=!printing; next} printing==0 {print}' "$MDX_FILE" | tail -n +2 > temp.md

              path_depth=$(awk -F'/' '{print NF-1}' <<< "$README_FILE")
              image_base_path=""
              for ((i=0; i<$path_depth; i++)); do
                image_base_path+="../"
              done

              sed -i "s|](/images/|](${image_base_path}docs/images/|g" temp.md

              echo "<!-- This file is auto-generated from $MDX_FILE. Do not edit manually. -->" | cat - temp.md > "$README_FILE"
              rm temp.md
              
              echo "Successfully generated $README_FILE"
            else
              echo "Warning: File $MDX_FILE not found, skipping..."
              ls -la "$(dirname "$MDX_FILE")"
            fi
          done

      - name: Debug file existence
        run: |
          echo "Checking for generated README files:"
          for dir in sdks/*/; do
            if [ -f "${dir}README.md" ]; then
              echo "✅ ${dir}README.md exists"
            else
              echo "❌ ${dir}README.md does not exist"
            fi
          done

      - name: Create Pull Request
        id: create_pr
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: "chore: sync docs to README files"
          title: "chore: sync docs to README files"
          body: |
            This PR was automatically created by the Sync Docs workflow.
          branch: sync-docs
          base: main
          committer: github-actions[bot] <41898282+github-actions[bot]@users.noreply.github.com>
          author: github-actions[bot] <41898282+github-actions[bot]@users.noreply.github.com>

      - name: Auto-merge PR
        if: steps.create_pr.outputs.pull-request-operation == 'created'
        run: |
          gh pr merge --squash --delete-branch --admin "$PR_URL"
        env:
          GITHUB_TOKEN: ${{ secrets.PAT_TOKEN }}
          PR_URL: ${{ steps.create_pr.outputs.pull-request-url }}
