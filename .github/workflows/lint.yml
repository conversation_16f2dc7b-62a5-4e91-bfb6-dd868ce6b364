name: <PERSON>t Check

on:
  push:
    branches: main
    paths:
      - 'web/frontend/**'
      - 'web/personas-open-source/**'
  pull_request:
    branches: main
    paths:
      - 'web/frontend/**'
      - 'web/personas-open-source/**'

jobs:
  lint-frontend:
    name: Lint Frontend
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./web/frontend
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: './web/frontend/package-lock.json'

      - name: Install dependencies
        run: npm ci

      - name: Run ESLint
        run: npm run lint

      - name: Check Prettier formatting
        run: npm run lint:format -- --check

  lint-personas:
    name: Lint Personas Open Source
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./web/personas-open-source
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: './web/personas-open-source/package-lock.json'

      - name: Install dependencies
        run: npm ci --legacy-peer-deps

      - name: Run ESLint
        run: npm run lint