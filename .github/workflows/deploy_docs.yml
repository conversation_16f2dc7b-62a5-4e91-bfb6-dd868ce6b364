# Simple workflow for deploying static content to GitHub Pages
name: Deploy static content to <PERSON><PERSON>

on:
  # thin<PERSON>'s comment: Disabled, use mintlify fn
  # Runs on pushes targeting the default branch
  # push:
  #  branches: [main]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

# Allow one concurrent deployment
concurrency:
  group: "pages"
  cancel-in-progress: true

env:
  # Hosted GitHub runners have 7 GB of memory available, let's use 6 GB
  NODE_OPTIONS: --max-old-space-size=6144

jobs:
  # Single deploy job since we're just deploying
  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Change to docs directory
        run: |
          cd docs
          pwd
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18.x
          cache: npm
          cache-dependency-path: docs/package-lock.json
      - name: Install dependencies
        working-directory: ./docs
        run: |
          pwd
          npm ci
      - name: Build
        working-directory: ./docs
        run: |
          pwd
          npm run build
        shell: /usr/bin/bash -e {0}
        env:
          NODE_OPTIONS: --max-old-space-size=6144
      - name: Setup Pages
        uses: actions/configure-pages@v3
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          # Upload docs build directory
          path: docs/build
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
