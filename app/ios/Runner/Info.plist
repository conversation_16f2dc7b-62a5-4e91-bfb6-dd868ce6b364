<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>ITSAppUsesNonExemptEncryption</key>
		<false/>
		<key>BGTaskSchedulerPermittedIdentifiers</key>
		<array>
			<string>daily-summary</string>
			<string>com.friend-app-with-wearable.ios12.daily-summary</string>
			<string>dev.flutter.background.refresh</string>
			<string>com.pravera.flutter_foreground_task.refresh</string>
		</array>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>$(BUNDLE_DISPLAY_NAME)</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>$(BUNDLE_NAME)</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string>h.omi.me</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>https</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string>h.omi.me</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>$(GOOGLE_REVERSE_CLIENT_ID)</string>
				</array>
			</dict>
		</array>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>FlutterDeepLinkingEnabled</key>
		<false/>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>comgooglemaps</string>
			<string>iosamap</string>
		</array>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>NSBluetoothAlwaysUsageDescription</key>
		<string>Bluetooth permission is required to connect with your Omi.</string>
		<key>NSBluetoothPeripheralUsageDescription</key>
		<string>Bluetooth permission is required to connect with your Omi.</string>
		<key>NSCalendarsFullAccessUsageDescription</key>
		<string>Access most functions for calendar viewing and editing.</string>
		<key>NSCalendarsUsageDescription</key>
		<string>Access most functions for calendar viewing and editing.</string>
		<key>NSContactsUsageDescription</key>
		<string>Access contacts for event attendee editing.</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>When creating your memories, we use your location for determining where they were created.</string>
		<key>NSLocationUsageDescription</key>
		<string>When creating your memories, we use your location for determining where they were created.</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>When creating your memories, we use your location for determining where they were created.</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>We need access to your microphone so you can share audio explanations of Bugs you find.</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>We need access to your photo library to allow you to upload and share photos through Instabug.</string>
		<key>NSRemindersUsageDescription</key>
		<string>We need access to your reminders to export action items from Omi conversations.</string>
		<key>PermissionGroupNotification</key>
		<string>You need to enable notifications to receive your pro-active feedback.</string>
		<key>NSCameraUsageDescription</key>
		<string>Camera access is required to report issues</string>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UIBackgroundModes</key>
		<array>
			<string>audio</string>
			<string>location</string>
			<string>bluetooth-central</string>
			<string>fetch</string>
			<string>processing</string>
			<string>remote-notification</string>
		</array>
		<key>UIDeviceFamily</key>
		<array>
			<integer>1</integer>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UIRequiresFullScreen</key>
		<true/>
		<key>UIStatusBarHidden</key>
		<false/>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
			<string>UIInterfaceOrientationPortrait</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false/>
		<key>com.posthog.posthog.AUTO_INIT</key>
    	<false/>
	</dict>
</plist>
