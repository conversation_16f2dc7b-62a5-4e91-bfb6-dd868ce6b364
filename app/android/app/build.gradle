plugins {
   id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    // END: FlutterFire Configuration
   id "kotlin-android"
   id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

def env = new Properties()
def envFile = rootProject.file(".env")
if (envFile.exists()) {
    envFile.withInputStream { stream ->
        env.load(stream)
    }
}


kotlin {
    jvmToolchain(21)
}

android {

    buildFeatures {
        buildConfig = true
    }

    namespace "com.friend.ios"


    // ----- BEGIN flavorDimensions (autogenerated by flutter_flavorizr) -----
    flavorDimensions += "flavor-type"

    productFlavors {
        prod {
            dimension "flavor-type"
            applicationId "com.friend.ios"
            resValue "string", "app_name", "Memorion"
        }
        dev {
            dimension "flavor-type"
            applicationId "com.friend.ios.dev"
            resValue "string", "app_name", "Memorion Dev"
        }
    }

    // ----- END flavorDimensions (autogenerated by flutter_flavorizr) -----

    compileSdkVersion 35

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_21
        targetCompatibility JavaVersion.VERSION_21
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    lintOptions {
        disable 'InvalidPackage'
        checkReleaseBuilds false
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.friend.ios"
        minSdkVersion 24
        targetSdkVersion 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled true
        buildConfigField("String", "INTERCOM_APP_ID", "\"${System.getenv("intercom.app.id")}\"")
        buildConfigField("String", "INTERCOM_ANDROID_API_KEY", "\"${System.getenv("intercom.android.api.key")}\"")
    }

    signingConfigs {
        release {
            if (System.getenv()["CI"]) { // CI=true is exported by Codemagic
                storeFile file(System.getenv()["CM_KEYSTORE_PATH"])
                storePassword System.getenv()["CM_KEYSTORE_PASSWORD"]
                keyAlias System.getenv()["CM_KEY_ALIAS"]
                keyPassword System.getenv()["CM_KEY_PASSWORD"]
            } else {
                keyAlias keystoreProperties['keyAlias']
                keyPassword keystoreProperties['keyPassword']
                storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
                storePassword keystoreProperties['storePassword']
            }
        }
        debug {
            if (keystorePropertiesFile.exists()) {
                keyAlias keystoreProperties['keyAlias']
                keyPassword keystoreProperties['keyPassword']
                storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
                storePassword keystoreProperties['storePassword']
            }
        }
    }

    buildTypes {
        release {
            productFlavors.dev.signingConfig signingConfigs.debug
            productFlavors.prod.signingConfig signingConfigs.release
            // fixing calendar
            // https://github.com/builttoroam/device_calendar/issues/99#issuecomment-612449677
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            minifyEnabled true
            shrinkResources true
        }
        debug{
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            minifyEnabled true
        }
    }

    ndkVersion System.getenv()["NDK_VERSION"] ?: "27.0.12077973"
}

flutter {
    source '../..'
}

dependencies {
    androidTestImplementation 'androidx.test:runner:1.2.0'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.0-alpha3'
    implementation 'io.intercom.android:intercom-sdk:latest.release'
}
