class McpApi<PERSON>ey {
  final String id;
  final String name;
  final String keyPrefix;
  final DateTime createdAt;
  final DateTime? lastUsedAt;

  McpApiKey({
    required this.id,
    required this.name,
    required this.keyPrefix,
    required this.createdAt,
    this.lastUsedAt,
  });

  factory McpApiKey.fromJson(Map<String, dynamic> json) {
    return McpApiKey(
      id: json['id'],
      name: json['name'],
      keyPrefix: json['key_prefix'],
      createdAt: DateTime.parse(json['created_at']),
      lastUsedAt: json['last_used_at'] != null ? DateTime.parse(json['last_used_at']) : null,
    );
  }
}

class Mcp<PERSON><PERSON><PERSON>eyCreated extends Mcp<PERSON><PERSON><PERSON>ey {
  final String key;

  McpApiKeyCreated({
    required super.id,
    required super.name,
    required super.keyPrefix,
    required super.createdAt,
    super.lastUsedAt,
    required this.key,
  });

  factory McpApiKeyCreated.from<PERSON><PERSON>(Map<String, dynamic> json) {
    return Mcp<PERSON><PERSON><PERSON>eyCreated(
      id: json['id'],
      name: json['name'],
      keyPrefix: json['key_prefix'],
      createdAt: DateTime.parse(json['created_at']),
      lastUsedAt: json['last_used_at'] != null ? DateTime.parse(json['last_used_at']) : null,
      key: json['key'],
    );
  }
}
