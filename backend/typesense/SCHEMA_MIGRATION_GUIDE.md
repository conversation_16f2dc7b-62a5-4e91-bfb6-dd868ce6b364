# Typesense Schema Migration Guide

## Overview
This guide explains the differences between the old `memories.schema` and the new `memories_new.schema` files, and how to migrate to the new schema that properly aligns with the Firebase database structure.

## Key Issues with the Old Schema

The original `memories.schema` file was missing many critical fields that exist in the Firebase database, causing sync errors when data is transferred from Firebase to Typesense. The old schema only included:

- `structured` (object)
- `structured.category` (string, facet)
- `transcript_segments` (object[])
- `created_at` (int64)
- `userId` (string)
- `discarded` (bool)

## New Schema Improvements

The new `memories_new.schema` includes all fields from the Firebase conversation documents:

### Core Fields
- `id` (string) - Conversation unique identifier
- `created_at` (int64) - Creation timestamp (Unix timestamp)
- `started_at` (int64, optional) - When conversation started
- `finished_at` (int64, optional) - When conversation ended
- `userId` (string) - User identifier for filtering

### Structured Data Fields
- `structured` (object) - Main structured conversation data
- `structured.title` (string, optional) - Conversation title
- `structured.overview` (string, optional) - Detailed conversation summary
- `structured.emoji` (string, optional) - Representative emoji
- `structured.category` (string, facet, optional) - Conversation category
- `structured.action_items` (object[], optional) - Extracted action items
- `structured.events` (object[], optional) - Calendar events

### Content Fields
- `transcript_segments` (object[], optional) - Conversation transcript
- `transcript_segments_compressed` (bool, optional) - Compression flag
- `photos` (object[], optional) - Associated photos
- `apps_results` (object[], optional) - App processing results
- `plugins_results` (object[], optional) - Plugin results (backward compatibility)

### Location Data
- `geolocation` (object, optional) - Location information
- `geolocation.latitude` (float, optional) - GPS latitude
- `geolocation.longitude` (float, optional) - GPS longitude
- `geolocation.address` (string, optional) - Human-readable address
- `geolocation.google_place_id` (string, optional) - Google Places ID
- `geolocation.location_type` (string, optional) - Type of location

### Metadata Fields
- `source` (string, facet, optional) - Conversation source (omi, friend, workflow, etc.)
- `language` (string, optional) - Conversation language
- `external_data` (object, optional) - External integration data
- `app_id` (string, optional) - Associated app ID
- `status` (string, facet, optional) - Processing status
- `discarded` (bool) - Whether conversation is discarded
- `deleted` (bool, optional) - Whether conversation is deleted
- `visibility` (string, optional) - Conversation visibility setting
- `processing_conversation_id` (string, optional) - Processing reference
- `processing_memory_id` (string, optional) - Memory processing reference
- `data_protection_level` (string, optional) - Data protection classification

## Search Functionality Compatibility

The new schema maintains full compatibility with existing search functionality:

### Current Search Implementation
The search function in `backend/utils/conversations/search.py` uses:
- `query_by`: `'structured.overview,structured.title'` ✅ Supported
- `filter_by`: `userId`, `discarded`, `created_at` ranges ✅ All supported
- `sort_by`: `'created_at:desc'` ✅ Supported

### Enhanced Search Capabilities
The new schema enables additional search and filtering options:
- Filter by `source` (faceted field)
- Filter by `status` (faceted field) 
- Filter by `structured.category` (faceted field)
- Geographic filtering using `geolocation` fields
- Date range filtering with `started_at` and `finished_at`

## Migration Steps

1. **Backup Current Data**: Export existing Typesense collection data
2. **Update Schema**: Replace the old schema with `memories_new.schema`
3. **Recreate Collection**: Drop and recreate the Typesense collection with new schema
4. **Update Firebase Extension**: Ensure the Firestore-Typesense extension syncs all required fields
5. **Resync Data**: Trigger a full resync from Firebase to populate all fields
6. **Test Search**: Verify search functionality works correctly

## Firebase Extension Configuration

Update your Firebase Typesense extension configuration to include all fields:

```
Firestore Collection Path: users/{userId}/conversations
Firestore Collection Fields: id,created_at,started_at,finished_at,structured,transcript_segments,transcript_segments_compressed,geolocation,photos,apps_results,plugins_results,source,language,external_data,app_id,status,discarded,deleted,visibility,processing_conversation_id,processing_memory_id,data_protection_level,userId
```

## Data Type Mappings

### Firebase → Typesense Type Mappings
- `string` → `string`
- `number` (timestamp) → `int64`
- `number` (float) → `float`
- `boolean` → `bool`
- `object` → `object`
- `array` → `object[]`

### Date Handling
- Firebase stores dates as ISO strings or Firestore timestamps
- Typesense expects Unix timestamps (int64) for date fields
- The sync process should convert dates to Unix timestamps

## Testing the New Schema

After migration, test the following:

1. **Basic Search**: Verify text search in `structured.overview` and `structured.title`
2. **Filtering**: Test filtering by `userId`, `discarded`, `source`, `status`
3. **Date Ranges**: Test date range filtering with `created_at`, `started_at`, `finished_at`
4. **Faceted Search**: Test faceted filtering by `structured.category`, `source`, `status`
5. **Sorting**: Verify sorting by `created_at` works correctly

## Troubleshooting

### Common Sync Errors
- **Field type mismatch**: Ensure date fields are converted to Unix timestamps
- **Missing required fields**: `id`, `created_at`, `userId`, `discarded` are required
- **Nested object issues**: Verify `enable_nested_fields: true` is set

### Performance Considerations
- Index frequently searched fields (`structured.title`, `structured.overview`)
- Use faceted fields for filtering (`structured.category`, `source`, `status`)
- Consider field weights for search relevance tuning
