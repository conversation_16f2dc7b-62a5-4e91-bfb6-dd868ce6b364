{"name": "conversations", "fields": [{"name": "id", "type": "string"}, {"name": "created_at", "type": "int64"}, {"name": "started_at", "type": "int64", "optional": true}, {"name": "finished_at", "type": "int64", "optional": true}, {"name": "structured", "type": "object"}, {"name": "structured.title", "type": "string", "optional": true}, {"name": "structured.overview", "type": "string", "optional": true}, {"name": "structured.emoji", "type": "string", "optional": true}, {"name": "structured.category", "type": "string", "facet": true, "optional": true}, {"name": "structured.action_items", "type": "object[]", "optional": true}, {"name": "structured.events", "type": "object[]", "optional": true}, {"name": "transcript_segments", "type": "object[]", "optional": true}, {"name": "transcript_segments_compressed", "type": "bool", "optional": true}, {"name": "geolocation", "type": "object", "optional": true}, {"name": "geolocation.latitude", "type": "float", "optional": true}, {"name": "geolocation.longitude", "type": "float", "optional": true}, {"name": "geolocation.address", "type": "string", "optional": true}, {"name": "geolocation.google_place_id", "type": "string", "optional": true}, {"name": "geolocation.location_type", "type": "string", "optional": true}, {"name": "photos", "type": "object[]", "optional": true}, {"name": "apps_results", "type": "object[]", "optional": true}, {"name": "plugins_results", "type": "object[]", "optional": true}, {"name": "source", "type": "string", "facet": true, "optional": true}, {"name": "language", "type": "string", "optional": true}, {"name": "external_data", "type": "object", "optional": true}, {"name": "app_id", "type": "string", "optional": true}, {"name": "status", "type": "string", "facet": true, "optional": true}, {"name": "discarded", "type": "bool"}, {"name": "deleted", "type": "bool", "optional": true}, {"name": "visibility", "type": "string", "optional": true}, {"name": "processing_conversation_id", "type": "string", "optional": true}, {"name": "processing_memory_id", "type": "string", "optional": true}, {"name": "data_protection_level", "type": "string", "optional": true}, {"name": "userId", "type": "string"}], "default_sorting_field": "created_at", "enable_nested_fields": true}